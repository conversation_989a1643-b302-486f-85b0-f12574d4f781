@echo off
echo ========================================
echo 启动AI配置页面测试环境
echo ========================================

echo.
echo 🚀 正在启动本地API服务器...
start "API服务器" cmd /k "cd /d %~dp0 && node local-api-server.cjs"

echo.
echo ⏳ 等待API服务器启动...
timeout /t 3 /nobreak >nul

echo.
echo 🌐 正在启动前端开发服务器...
start "前端服务器" cmd /k "cd /d %~dp0 && npm run dev"

echo.
echo ⏳ 等待前端服务器启动...
timeout /t 5 /nobreak >nul

echo.
echo ✅ 测试环境启动完成！
echo.
echo 📡 API服务器: http://localhost:3000/admin
echo 🌐 前端应用: http://localhost:5173
echo.
echo 🧪 测试步骤：
echo 1. 打开浏览器访问 http://localhost:5173
echo 2. 进入AI配置页面
echo 3. 测试以下功能：
echo    - 添加AI模型
echo    - 编辑AI模型
echo    - 删除AI模型
echo    - 测试AI连接
echo    - 保存系统配置
echo.
echo 💡 提示：
echo - 所有数据都是模拟数据，重启后会重置
echo - 可以在API服务器窗口查看请求日志
echo - 按Ctrl+C可以停止对应的服务器
echo.
echo 按任意键关闭此窗口...
pause >nul
