// 数据查询云函数
// 专门用于管理后台查询小程序数据库

const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 通用响应格式
const createResponse = (code = 200, message = 'success', data = null) => {
  return {
    code,
    message,
    data,
    timestamp: new Date().toISOString()
  }
}

// 数据查询处理器
const dataHandlers = {
  // 获取总用户数
  async getTotalUsers() {
    try {
      const result = await db.collection('users').count()
      return result.total || 0
    } catch (error) {
      console.error('查询总用户数失败:', error)
      return 0
    }
  },

  // 获取活跃教师用户数
  async getActiveTeachers() {
    try {
      // 🔥 先尝试查询有lastLoginTime的活跃用户
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      
      const activeResult = await db.collection('users')
        .where({
          lastLoginTime: db.command.gte(thirtyDaysAgo)
        })
        .count()
      
      // 如果有活跃用户，返回活跃用户数
      if (activeResult.total > 0) {
        return activeResult.total
      }
      
      // 🔥 如果没有活跃用户记录，返回总用户数作为估算
      console.log('没有lastLoginTime记录，使用总用户数估算活跃用户')
      return await this.getTotalUsers()
    } catch (error) {
      console.error('查询活跃教师失败，返回总用户数:', error)
      return await this.getTotalUsers()
    }
  },

  // 获取评语总数
  async getTotalComments() {
    try {
      const result = await db.collection('comments').count()
      return result.total || 0
    } catch (error) {
      console.error('查询评语总数失败:', error)
      return 0
    }
  },

  // 获取今日评语数
  async getTodayComments() {
    try {
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      
      const result = await db.collection('comments')
        .where({
          createTime: db.command.gte(today)
        })
        .count()
      
      return result.total || 0
    } catch (error) {
      console.error('查询今日评语数失败:', error)
      return 0
    }
  },

  // 获取AI调用总数
  async getTotalAICalls() {
    try {
      // 尝试从ai_usage集合查询
      try {
        const result = await db.collection('ai_usage').count()
        return result.total || 0
      } catch (aiError) {
        // 如果没有ai_usage集合，从comments集合估算
        const commentsCount = await this.getTotalComments()
        return commentsCount * 2 // 假设每个评语需要2次AI调用
      }
    } catch (error) {
      console.error('查询AI调用总数失败:', error)
      return 0
    }
  },

  // 获取学生总数
  async getTotalStudents() {
    try {
      const result = await db.collection('students').count()
      return result.total || 0
    } catch (error) {
      console.error('查询学生总数失败:', error)
      return 0
    }
  },

  // 获取仪表板统计数据
  async getDashboardStats() {
    try {
      const [totalUsers, todayComments, aiCalls, studentTotal] = await Promise.all([
        this.getActiveTeachers(),
        this.getTodayComments(),
        this.getTotalAICalls(),
        this.getTotalStudents()
      ])
      
      return {
        totalUsers,
        todayComments,
        aiCalls,
        totalStudents: studentTotal, // 🔥 修复字段名匹配
        satisfaction: 4.8,
        lastUpdated: new Date().toISOString()
      }
    } catch (error) {
      console.error('获取仪表板统计失败:', error)
      return {
        totalUsers: 0,
        todayComments: 0,
        aiCalls: 0,
        totalStudents: 0, // 🔥 修复字段名匹配
        satisfaction: 0,
        lastUpdated: new Date().toISOString()
      }
    }
  },

  // 获取最近活动记录
  async getRecentActivities(limit = 10) {
    try {
      const commentsResult = await db.collection('comments')
        .orderBy('createTime', 'desc')
        .limit(limit)
        .get()
      
      return commentsResult.data.map(comment => ({
        id: comment._id,
        userId: comment.openid || comment.userId || 'unknown',
        userName: comment.teacherName || comment.userName || '未知用户',
        action: `为学生"${comment.studentName || '未知学生'}"生成评语`,
        actionType: 'comment_generate',
        timestamp: comment.createTime || new Date().toISOString(),
        metadata: {
          studentName: comment.studentName,
          subject: comment.subject || '未知科目',
          contentLength: comment.content ? comment.content.length : 0
        }
      }))
    } catch (error) {
      console.error('查询最近活动失败:', error)
      return []
    }
  },

  // 获取学生数据
  async getStudents(params = {}) {
    try {
      const { page = 1, limit = 20, keyword = '' } = params
      const skip = (page - 1) * limit
      
      let query = db.collection('students')
      
      if (keyword) {
        query = query.where({
          name: db.RegExp({
            regexp: keyword,
            options: 'i'
          })
        })
      }
      
      const countResult = await query.count()
      const dataResult = await query
        .orderBy('updateTime', 'desc')
        .skip(skip)
        .limit(limit)
        .get()
      
      const students = dataResult.data.map(student => ({
        id: student._id,
        name: student.name || student.studentName,
        class: student.class || student.className,
        teacher: student.teacher || student.teacherName,
        commentsCount: student.commentsCount || 0,
        lastUpdate: student.updateTime || student.lastUpdate || new Date().toISOString(),
        status: student.status || 'active'
      }))
      
      return {
        list: students,
        total: countResult.total,
        page,
        limit
      }
    } catch (error) {
      console.error('查询学生数据失败:', error)
      return {
        list: [],
        total: 0,
        page: 1,
        limit: 20
      }
    }
  },

  // 获取评语记录
  async getComments(params = {}) {
    try {
      const { page = 1, limit = 20, teacherId = '', studentId = '' } = params
      const skip = (page - 1) * limit
      
      let query = db.collection('comments')
      
      const whereConditions = {}
      if (teacherId) whereConditions.openid = teacherId
      if (studentId) whereConditions.studentId = studentId
      
      if (Object.keys(whereConditions).length > 0) {
        query = query.where(whereConditions)
      }
      
      const countResult = await query.count()
      const dataResult = await query
        .orderBy('createTime', 'desc')
        .skip(skip)
        .limit(limit)
        .get()
      
      const comments = dataResult.data.map(comment => ({
        id: comment._id,
        studentId: comment.studentId,
        studentName: comment.studentName,
        teacherId: comment.openid || comment.teacherId,
        teacherName: comment.teacherName,
        content: comment.content,
        aiModel: comment.aiModel || comment.model || 'doubao',
        tokensUsed: comment.tokensUsed || comment.tokens || 0,
        createTime: comment.createTime,
        subject: comment.subject
      }))
      
      return {
        list: comments,
        total: countResult.total,
        page,
        limit
      }
    } catch (error) {
      console.error('查询评语记录失败:', error)
      return {
        list: [],
        total: 0,
        page: 1,
        limit: 20
      }
    }
  },

  // 获取单个学生信息
  async getStudent(studentId) {
    try {
      if (!studentId) {
        throw new Error('学生ID不能为空')
      }
      
      const result = await db.collection('students').doc(studentId).get()
      
      if (!result.data) {
        return null
      }
      
      const student = result.data
      return {
        id: student._id,
        name: student.name || student.studentName,
        class: student.class || student.className,
        teacher: student.teacher || student.teacherName,
        commentsCount: student.commentsCount || 0,
        lastUpdate: student.updateTime || student.lastUpdate || new Date().toISOString(),
        status: student.status || 'active'
      }
    } catch (error) {
      console.error('查询学生信息失败:', error)
      return null
    }
  },

  // 获取单个评语信息
  async getComment(commentId) {
    try {
      if (!commentId) {
        throw new Error('评语ID不能为空')
      }
      
      const result = await db.collection('comments').doc(commentId).get()
      
      if (!result.data) {
        return null
      }
      
      const comment = result.data
      return {
        id: comment._id,
        studentId: comment.studentId,
        studentName: comment.studentName,
        teacherId: comment.openid || comment.teacherId,
        teacherName: comment.teacherName,
        content: comment.content,
        aiModel: comment.aiModel || comment.model || 'doubao',
        tokensUsed: comment.tokensUsed || comment.tokens || 0,
        createTime: comment.createTime,
        subject: comment.subject
      }
    } catch (error) {
      console.error('查询评语信息失败:', error)
      return null
    }
  },

  // 获取班级列表
  async getClasses(params = {}) {
    try {
      const { page = 1, limit = 20, teacherId = '' } = params
      const skip = (page - 1) * limit
      
      let query = db.collection('classes')
      
      if (teacherId) {
        query = query.where({ teacherId })
      }
      
      const countResult = await query.count()
      const dataResult = await query
        .orderBy('createTime', 'desc')
        .skip(skip)
        .limit(limit)
        .get()
      
      const classes = dataResult.data.map(classInfo => ({
        id: classInfo._id,
        name: classInfo.name || classInfo.className,
        grade: classInfo.grade,
        teacherId: classInfo.teacherId,
        teacherName: classInfo.teacherName,
        studentCount: classInfo.studentCount || 0,
        description: classInfo.description,
        createTime: classInfo.createTime,
        status: classInfo.status || 'active'
      }))
      
      return {
        list: classes,
        total: countResult.total,
        page,
        limit
      }
    } catch (error) {
      console.error('查询班级数据失败:', error)
      return {
        list: [],
        total: 0,
        page: 1,
        limit: 20
      }
    }
  },

  // 测试数据库连接
  async testConnection() {
    try {
      const result = await db.collection('users').limit(1).get()
      
      return {
        success: true,
        message: '数据库连接正常（云函数环境）',
        collections: ['users', 'students', 'comments'],
        sampleData: result.data.length > 0 ? result.data[0] : null
      }
    } catch (error) {
      console.error('数据库连接测试失败:', error)
      return {
        success: false,
        message: error.message,
        collections: [],
        sampleData: null
      }
    }
  }
}

// 主函数
exports.main = async (event, context) => {
  console.log('📥 DataQuery 收到请求:', event)
  
  try {
    const { action, params = {} } = event
    
    if (!action) {
      throw new Error('缺少action参数')
    }
    
    let result = null
    
    switch (action) {
      case 'getDashboardStats':
        result = await dataHandlers.getDashboardStats()
        break
        
      case 'getRecentActivities':
        result = await dataHandlers.getRecentActivities(params.limit)
        break
        
      case 'getStudents':
        result = await dataHandlers.getStudents(params)
        break
        
      case 'getComments':
        result = await dataHandlers.getComments(params)
        break
        
      case 'testConnection':
        result = await dataHandlers.testConnection()
        break
        
      case 'getStudent':
        result = await dataHandlers.getStudent(params.id)
        break
        
      case 'getComment':
        result = await dataHandlers.getComment(params.id)
        break
        
      case 'getClasses':
        result = await dataHandlers.getClasses(params)
        break
        
      default:
        throw new Error(`未知的action: ${action}`)
    }
    
    console.log('✅ DataQuery 处理成功:', action)
    return createResponse(200, 'success', result)
    
  } catch (error) {
    console.error('❌ DataQuery 处理失败:', error)
    return createResponse(500, error.message, null)
  }
}
